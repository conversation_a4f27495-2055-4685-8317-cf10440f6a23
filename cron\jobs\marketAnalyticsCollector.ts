/**
 * Market Analytics Data Collector
 *
 * This module collects and aggregates job market data from job postings
 * and stores it in the JobMarketMetrics and SkillTrend tables in the cron schema.
 */

console.log("🚀 Starting market analytics collector script...");

import { logger } from "../utils/logger";
import { getPrismaClient } from "../utils/prismaClient";
import { withJobLock } from "../utils/redisJobLock";
import { CircuitState } from "../utils/improvedImprovedCircuitBreaker";
import { redis } from "../utils/redis";

// Initialize Prisma client variable
let prisma: any;

// Define types for better type safety
interface Occupation {
  id: string;
  title: string;
  category?: string;
}

interface JobPosting {
  id: string;
  title: string;
  company: string;
  location?: string;
  remoteType?: string;
  salaryMin?: number;
  salaryMax?: number;
  skills?: string[];
  createdAt: Date;
}

interface SalaryData {
  min: number;
  max: number;
  avg: number;
}

interface SalaryRange {
  min: number;
  max: number;
  median: number;
}

interface SkillCount {
  name: string;
  count: number;
}

interface CompanyCount {
  name: string;
  count: number;
}

/**
 * Main function to collect market analytics data
 */
export async function collectMarketAnalytics() {
  try {
    logger.info("Starting market analytics data collection");

    // Initialize Prisma client
    prisma = await getPrismaClient("web");
    logger.info("✅ Prisma client initialized");

    // Set previous date to 30 days ago
    const previousDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    logger.info(`Previous date: ${previousDate.toISOString()}`);

    let occupationsProcessed = 0;
    let occupationsSucceeded = 0;
    let occupationsFailed = 0;

    try {
      // Get all occupations from the cron schema
      const occupations = await prisma.occupations.findMany({
        select: { id: true, title: true, category: true },
      });
      console.log("Occupations found:", occupations.length);
      occupationsProcessed = occupations.length;

      // Process each occupation
      for (const occupation of occupations) {
        try {
          await processOccupation(occupation, previousDate);
          occupationsSucceeded++;
        } catch (error) {
          console.error(
            `Error processing occupation ${occupation.title}:`,
            error
          );
          occupationsFailed++;
        }
      }
    } catch (error) {
      console.error("Error getting occupations:", error);
    }

    console.log("Market analytics data collection completed");
    console.log(
      `Processed: ${occupationsProcessed}, Succeeded: ${occupationsSucceeded}, Failed: ${occupationsFailed}`
    );

    logger.info("Market analytics data collection completed");
    logger.info(
      `[JOB_STATS] MARKETANALYTICS | PROCESSED:${occupationsProcessed} | SUCCEEDED:${occupationsSucceeded} | FAILED:${occupationsFailed}`
    );
  } catch (error) {
    console.error("Error collecting market analytics data:", error);
    logger.error("Error collecting market analytics data:", error);
    throw error;
  } finally {
    if (prisma) {
      await prisma.$disconnect().catch((err: Error) => {
        logger.error("Error disconnecting Prisma:", err);
      });
    }
  }
}

/**
 * Process job postings for a specific occupation
 */
async function processOccupation(occupation: Occupation, previousDate: Date) {
  try {
    console.log(`Processing occupation: ${occupation.title}`);

    // Get job postings for this occupation
    // Note: Currently no jobs have isProcessing=true, so we're not filtering on it
    const jobPostings = await prisma.jobListing.findMany({
      where: {
        title: { contains: occupation.title, mode: "insensitive" },
        createdAt: { gte: previousDate },
        isActive: true,
        // isProcessing: true, // Uncomment this when jobs are properly marked as processed
      },
      select: {
        id: true,
        title: true,
        company: true,
        location: true,
        salaryMin: true,
        salaryMax: true,
        skills: true,
        createdAt: true,
      },
    });

    console.log(
      `Found ${jobPostings.length} job postings for occupation: ${occupation.title}`
    );

    if (jobPostings.length === 0) {
      logger.info(`No job postings found for occupation: ${occupation.title}`);
      return;
    }

    // Count remote jobs - using an integer count as per requirements
    const remoteCount = jobPostings.filter((job: { location: string }) =>
      job.location?.toLowerCase().includes("remote")
    ).length;

    // Calculate salary statistics
    const salaries: SalaryData[] = jobPostings
      .filter(
        (job: { salaryMin: any; salaryMax: any }) =>
          job.salaryMin && job.salaryMax
      )
      .map((job: { salaryMin: any; salaryMax: any }) => ({
        min: job.salaryMin!,
        max: job.salaryMax!,
        avg: (job.salaryMin! + job.salaryMax!) / 2,
      }));

    const avgSalary =
      salaries.length > 0
        ? salaries.reduce((sum, s) => sum + s.avg, 0) / salaries.length
        : null;

    const salaryRange: SalaryRange | null =
      salaries.length > 0
        ? {
            min: Math.min(...salaries.map((s) => s.min)),
            max: Math.max(...salaries.map((s) => s.max)),
            median: getMedian(salaries.map((s) => s.avg)),
          }
        : null;

    // Extract skills from job postings
    const skillsMap = new Map<string, number>();

    jobPostings.forEach((job: { skills: any[] }) => {
      if (job.skills && job.skills.length > 0) {
        job.skills.forEach((skill) => {
          const count = skillsMap.get(skill) || 0;
          skillsMap.set(skill, count + 1);
        });
      }
    });

    // Convert to array and sort by frequency
    const topSkills: SkillCount[] = Array.from(skillsMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20); // Top 20 skills

    // Extract top companies
    const companiesMap = new Map<string, number>();

    jobPostings.forEach((job: { company: string }) => {
      if (job.company) {
        const count = companiesMap.get(job.company) || 0;
        companiesMap.set(job.company, count + 1);
      }
    });

    // Convert to array and sort by frequency
    const topCompanies: CompanyCount[] = Array.from(companiesMap.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 companies

    console.log(`Storing metrics for occupation: ${occupation.title}`);

    try {
      // Store metrics in database using direct SQL with proper UUID generation and JSON casting
      await prisma.$executeRaw`
        INSERT INTO "cron"."JobMarketMetrics" (
          "id", "occupationId", "remoteCount", "totalCount", "avgSalary",
          "salaryRange", "topSkills", "topCompanies", "collectedAt"
        ) VALUES (
          gen_random_uuid(), ${occupation.id}, ${remoteCount}, ${jobPostings.length}, ${avgSalary},
          ${JSON.stringify(salaryRange)}::jsonb, ${JSON.stringify(topSkills)}::jsonb, ${JSON.stringify(topCompanies)}::jsonb, NOW()
        )
      `;

      console.log(`Metrics stored for occupation: ${occupation.title}`);
    } catch (error) {
      console.error(
        `Error storing metrics for occupation ${occupation.title}:`,
        error
      );
    }

    // Process skill trends
    await processSkillTrends(occupation.id, skillsMap, previousDate);

    // TODO: Once isAnalyzed column is added, mark jobs as analyzed
    if (jobPostings.length > 0) {
      console.log(`Processed ${jobPostings.length} jobs for market analytics`);
      // After migration is applied, uncomment the following code:
      // const jobIds = jobPostings.map((job) => job.id);
      // await prisma.jobListing.updateMany({
      //   where: { id: { in: jobIds } },
      //   data: { isAnalyzed: true },
      // });
    }

    console.log(
      `Processed ${jobPostings.length} job postings for occupation: ${occupation.title}`
    );
    logger.info(
      `Processed ${jobPostings.length} job postings for occupation: ${occupation.title}`
    );
  } catch (error) {
    console.error(`Error processing occupation ${occupation.title}:`, error);
    logger.error(`Error processing occupation ${occupation.title}:`, error);
    throw error;
  }
}

/**
 * Process skill trends for a specific occupation
 */
async function processSkillTrends(
  occupationId: string,
  skillsMap: Map<string, number>,
  previousDate: Date
) {
  try {
    console.log(`Processing skill trends for occupation: ${occupationId}`);

    // Get previous skill trends using direct SQL
    const previousSkillTrends = (await prisma.$queryRaw`
      SELECT "skillName", "mentionCount"
      FROM "cron"."SkillTrend"
      WHERE "occupationId" = ${occupationId}
      AND "collectedAt" >= ${previousDate}
    `) as { skillName: string; mentionCount: number }[];

    console.log(`Found ${previousSkillTrends.length} previous skill trends`);

    // Create a map of previous skill counts
    const previousSkillCounts = new Map<string, number>();
    previousSkillTrends.forEach((trend) => {
      previousSkillCounts.set(trend.skillName, trend.mentionCount);
    });

    // Process each skill
    for (const [skillName, mentionCount] of skillsMap.entries()) {
      const previousCount = previousSkillCounts.get(skillName) || 0;

      // Calculate growth rate
      const growthRate =
        previousCount > 0
          ? ((mentionCount - previousCount) / previousCount) * 100
          : null;

      try {
        // Store skill trend using direct SQL with proper UUID generation
        await prisma.$executeRaw`
          INSERT INTO "cron"."SkillTrend" (
            "id", "skillName", "occupationId", "mentionCount", "growthRate", "collectedAt"
          ) VALUES (
            gen_random_uuid(), ${skillName}, ${occupationId}, ${mentionCount}, ${growthRate}, NOW()
          )
        `;
      } catch (error) {
        console.error(`Error storing skill trend for ${skillName}:`, error);
      }
    }

    console.log(
      `Processed ${skillsMap.size} skill trends for occupation: ${occupationId}`
    );
  } catch (error) {
    console.error(
      `Error processing skill trends for occupation ${occupationId}:`,
      error
    );
    logger.error(
      `Error processing skill trends for occupation ${occupationId}:`,
      error
    );
  }
}

/**
 * Calculate the median value of an array
 */
function getMedian(values: number[]): number {
  if (values.length === 0) return 0;

  const sorted = [...values].sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  }

  return sorted[middle];
}

// Export the main function for use in the cron job
export default collectMarketAnalytics;

/**
 * Main function to run the job with proper cleanup and locking
 */

/**
 * Get the current circuit breaker state from Redis
 */
async function getCircuitBreakerState(): Promise<CircuitState> {
  try {
    const state = await redis.get("circuit_breaker:state");
    return (state as CircuitState) || CircuitState.CLOSED;
  } catch (error) {
    logger.error(
      `❌ Error getting circuit breaker state: ${error instanceof Error ? error.message : String(error)}`
    );
    return CircuitState.CLOSED; // Default to CLOSED if we can't get the state
  }
}

async function main() {
  try {
    // Check circuit breaker state first

    const circuitBreakerState = await getCircuitBreakerState();

    if (circuitBreakerState === CircuitState.OPEN) {
      logger.error("❌ Circuit breaker is OPEN, skipping job execution");

      return 1;
    }

    // Use the Redis-based job lock
    const result = await withJobLock(
      "market_analytics",
      3600, // 1 hour lock timeout
      collectMarketAnalytics
    );

    // If the job was skipped due to lock, log it and return
    if (result === null) {
      logger.warn("🔒 Market analytics job was skipped due to existing lock");
      return 0;
    }

    logger.info("✅ Market analytics job completed successfully");
    return 0;
  } catch (error: any) {
    logger.error("❌ Error running market analytics job:", error);
    return 1;
  }
}

// Run the main function directly
logger.info("Running market analytics collector directly");
main()
  .then((exitCode) => {
    process.exit(exitCode);
  })
  .catch((error) => {
    logger.error("Unhandled error in main:", error);
    process.exit(1);
  });
