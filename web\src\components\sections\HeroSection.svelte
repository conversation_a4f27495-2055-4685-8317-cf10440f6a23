<script>
  import { fly } from 'svelte/transition';
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Clock,
    Target,
    Zap,
    X,
    Search,
    FileText,
    Send,
    ArrowLeftRight,
    ArrowRight,
  } from 'lucide-svelte';

  let visible = $state(false);
  let automationStep = $state(0);
  let sliderPosition = $state(0); // 0 = show chaos, 100 = show automation
  let isDragging = $state(false);

  $effect(() => {
    visible = true;

    // Automation step animations
    const automationTimer = setInterval(() => {
      automationStep = (automationStep + 1) % 4;
    }, 2500);

    // Auto-slide demo every 6 seconds
    const slideTimer = setInterval(() => {
      if (!isDragging) {
        sliderPosition = sliderPosition === 0 ? 100 : 0;
      }
    }, 6000);

    return () => {
      clearInterval(automationTimer);
      clearInterval(slideTimer);
    };
  });

  // Handle slider drag
  function handleSliderDrag(event) {
    if (!isDragging) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    sliderPosition = percentage;
  }

  function startDrag() {
    isDragging = true;
  }

  function stopDrag() {
    isDragging = false;
  }
</script>

<section class="relative overflow-hidden bg-white dark:bg-slate-900">
  <!-- Background Pattern -->
  <svg
    aria-hidden="true"
    class="absolute inset-0 h-full w-full stroke-slate-200 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)] dark:stroke-slate-700">
    <defs>
      <pattern
        x="50%"
        y="-1"
        id="hero-pattern"
        width="200"
        height="200"
        patternUnits="userSpaceOnUse">
        <path d="M.5 200V.5H200" fill="none" />
      </pattern>
    </defs>
    <svg x="50%" y="-1" class="overflow-visible fill-slate-50 dark:fill-slate-800/20">
      <path
        d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
        stroke-width="0" />
    </svg>
    <rect fill="url(#hero-pattern)" width="100%" height="100%" stroke-width="0" />
  </svg>

  <!-- Decorative Background Shape -->
  <div
    aria-hidden="true"
    class="absolute left-[calc(50%-4rem)] top-10 -z-10 transform-gpu blur-3xl sm:left-[calc(50%-18rem)] lg:left-48 lg:top-[calc(50%-30rem)] xl:left-[calc(50%-24rem)]">
    <div
      class="aspect-[1108/632] w-[69.25rem] bg-gradient-to-r from-[#80caff] to-[#4f46e5] opacity-20"
      style="clip-path: polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)">
    </div>
  </div>

  <div class="relative">
    <div
      class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40">
      <div class="mx-auto max-w-2xl lg:mx-0 lg:flex-auto">
        {#if visible}
          <h1
            in:fly={{ y: 30, duration: 1000 }}
            class="mt-10 max-w-lg text-4xl font-bold tracking-tight text-slate-900 sm:text-6xl dark:text-white">
            We're changing the way people
            <span
              class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              find jobs
            </span>
          </h1>
          <p
            in:fly={{ y: 30, duration: 1000, delay: 200 }}
            class="mt-6 text-lg leading-8 text-slate-600 dark:text-slate-300">
            Stop struggling with manual job applications. Our AI automation finds, matches, and
            applies to jobs for you while you focus on what matters most - preparing for interviews
            and landing your dream role.
          </p>
          <div
            in:fly={{ y: 30, duration: 1000, delay: 400 }}
            class="mt-10 flex items-center gap-x-6">
            <Button
              class="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
              Get started
            </Button>
            <Button
              variant="ghost"
              class="text-sm font-semibold leading-6 text-slate-900 dark:text-white">
              Live demo <ArrowRight class="ml-1 h-4 w-4" />
            </Button>
          </div>
        {/if}
      </div>

      <!-- Image Grid Section -->
      <div class="mt-16 sm:mt-24 lg:mt-0 lg:flex-shrink-0 lg:flex-grow">
        {#if visible}
          <div
            in:fly={{ y: 50, duration: 1000, delay: 600 }}
            class="mx-auto flex w-full max-w-2xl flex-col gap-6 sm:gap-8 lg:mx-0 lg:max-w-none lg:flex-row">
            <!-- Left Column -->
            <div class="flex flex-col gap-6 sm:gap-8">
              <!-- Large Image -->
              <div
                class="relative overflow-hidden rounded-xl bg-slate-900/5 ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                <div
                  class="relative h-80 cursor-grab overflow-hidden rounded-xl bg-white shadow-inner active:cursor-grabbing lg:h-96"
                  onmousemove={handleSliderDrag}
                  onmouseup={stopDrag}
                  onmouseleave={stopDrag}
                  role="slider"
                  tabindex="0"
                  aria-valuemin="0"
                  aria-valuemax="100"
                  aria-valuenow={sliderPosition}
                  aria-label="Drag to compare manual vs automated job hunting">
                  <!-- Modern Browser Window -->
                  <div class="absolute inset-0 bg-white">
                    <!-- Modern Browser Header -->
                    <div
                      class="flex items-center justify-between border-b border-slate-200 bg-white px-3 py-2">
                      <div class="flex items-center space-x-2">
                        <div class="h-2.5 w-2.5 rounded-full bg-red-500"></div>
                        <div class="h-2.5 w-2.5 rounded-full bg-yellow-500"></div>
                        <div class="h-2.5 w-2.5 rounded-full bg-green-500"></div>
                      </div>
                      <div class="mx-4 flex-1">
                        <div
                          class="mx-auto max-w-xs rounded bg-slate-100 px-3 py-1 text-xs text-slate-600">
                          indeed.com/jobs?q=software...
                        </div>
                      </div>
                      <div class="text-xs text-red-600">47 tabs</div>
                    </div>

                    <!-- Modern Browser Tabs (Chrome-style) -->
                    <div class="flex overflow-hidden border-b border-slate-200 bg-slate-50">
                      {#each Array(8) as _, i}
                        <div
                          class="flex min-w-0 max-w-48 items-center border-r border-slate-200 bg-white px-3 py-2
                      {i === 0 ? 'bg-white' : 'bg-slate-50 hover:bg-slate-100'}
                      {i < 2 ? 'border-b-2 border-red-400' : ''}">
                          <!-- Favicon -->
                          <div
                            class="mr-2 h-4 w-4 flex-shrink-0 rounded-sm bg-gradient-to-br from-blue-500 to-blue-600">
                          </div>
                          <!-- Tab title -->
                          <span class="truncate text-xs text-slate-700">
                            {[
                              'Indeed - Software Engineer Jobs',
                              'LinkedIn - Job Search',
                              'Glassdoor - Company Reviews',
                              'Monster - Find Jobs',
                              'ZipRecruiter - Apply Now',
                              'AngelList - Startup Jobs',
                              'Remote.co - Remote Work',
                              'FlexJobs - Flexible Work',
                            ][i]}
                          </span>
                          <!-- Close button -->
                          <div
                            class="ml-2 flex h-4 w-4 flex-shrink-0 items-center justify-center rounded hover:bg-slate-200">
                            <div class="h-2 w-2 text-slate-400">×</div>
                          </div>
                        </div>
                      {/each}

                      <!-- Overflow indicator -->
                      <div class="flex items-center border-l border-red-200 bg-red-50 px-3 py-2">
                        <span class="text-xs font-medium text-red-600">+39 more tabs</span>
                      </div>
                    </div>

                    <!-- Browser Content with Sticky Notes -->
                    <div
                      class="relative h-full bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 p-4">
                      <!-- Error Messages and Chaos -->
                      <div class="mb-4 space-y-3">
                        <div
                          class="rounded-lg border border-red-300 bg-gradient-to-r from-red-50 to-red-100 p-3 shadow-md">
                          <div class="flex items-center text-red-700">
                            <X class="mr-2 h-4 w-4" />
                            <div>
                              <div class="font-semibold">
                                Application Error - Session timeout after 2 hours
                              </div>
                              <div class="mt-1 text-xs text-red-600">
                                Lost all form data. Need to start over...
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          class="rounded-lg border border-yellow-300 bg-gradient-to-r from-yellow-50 to-yellow-100 p-3 shadow-md">
                          <div class="text-yellow-800">
                            <div class="font-semibold">
                              📄 Cover_Letter_Template_v47_FINAL_FINAL_USE_THIS.docx
                            </div>
                            <div class="mt-1 text-xs text-yellow-700">
                              Last modified: 3 hours ago
                            </div>
                          </div>
                        </div>
                        <div
                          class="rounded-lg border border-orange-300 bg-gradient-to-r from-orange-50 to-orange-100 p-3 shadow-md">
                          <div class="text-orange-800">
                            <div class="font-semibold">
                              📊 Job_Applications_Tracker_2024_UPDATED_v3_REAL.xlsx
                            </div>
                            <div class="mt-1 text-xs text-orange-700">
                              Status: Completely out of sync
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Lots of Sticky Notes scattered everywhere -->
                      <div
                        class="absolute left-6 top-16 z-10 -rotate-3 transform rounded bg-yellow-300 p-2 text-xs shadow-md">
                        🔔 Follow up Google!
                      </div>
                      <div
                        class="absolute right-8 top-20 z-10 rotate-2 transform rounded bg-pink-300 p-2 text-xs shadow-md">
                        ✏️ Update resume
                      </div>
                      <div
                        class="absolute left-12 top-32 z-10 -rotate-1 transform rounded bg-blue-300 p-2 text-xs shadow-md">
                        📝 Applied to 5 jobs
                      </div>
                      <div
                        class="absolute right-16 top-40 z-10 rotate-3 transform rounded bg-green-300 p-2 text-xs shadow-md">
                        📚 Interview prep
                      </div>
                      <div
                        class="absolute left-1/3 top-24 z-10 rotate-1 transform rounded bg-purple-300 p-2 text-xs shadow-md">
                        � Salary research
                      </div>
                      <div
                        class="absolute bottom-20 left-8 z-10 -rotate-2 transform rounded bg-orange-300 p-2 text-xs shadow-md">
                        📞 Call recruiter
                      </div>
                      <div
                        class="absolute bottom-16 right-12 z-10 rotate-1 transform rounded bg-red-300 p-2 text-xs shadow-md">
                        ⏰ Deadline today!
                      </div>
                      <div
                        class="absolute left-1/4 top-1/2 z-10 -rotate-1 transform rounded bg-cyan-300 p-2 text-xs shadow-md">
                        🎯 Target companies
                      </div>
                      <div
                        class="absolute right-1/4 top-1/2 z-10 rotate-2 transform rounded bg-lime-300 p-2 text-xs shadow-md">
                        📊 Track applications
                      </div>
                      <div
                        class="absolute bottom-1/3 left-1/2 z-10 -rotate-3 transform rounded bg-indigo-300 p-2 text-xs shadow-md">
                        🔍 Job search tips
                      </div>

                      <!-- Stress Indicator -->
                      <div
                        class="absolute bottom-4 left-4 right-4 rounded border border-red-300 bg-red-100 p-2">
                        <div class="flex items-center text-xs text-red-700">
                          <Clock class="mr-1 h-3 w-3" />
                          <span class="font-semibold">6 hours • 3 applications • 0 responses</span>
                        </div>
                      </div>
                    </div>
                    <!-- Automation Dashboard Overlay -->
                    <div
                      class="absolute inset-0 bg-white transition-transform duration-700 ease-in-out"
                      style="transform: translateX({sliderPosition}%)">
                      <!-- Dashboard Header -->
                      <div
                        class="flex items-center justify-between border-b border-slate-200 bg-white px-3 py-2">
                        <div class="flex items-center space-x-2">
                          <div class="h-2.5 w-2.5 rounded-full bg-red-500"></div>
                          <div class="h-2.5 w-2.5 rounded-full bg-yellow-500"></div>
                          <div class="h-2.5 w-2.5 rounded-full bg-green-500"></div>
                        </div>
                        <div class="mx-4 flex-1">
                          <div
                            class="mx-auto max-w-xs rounded bg-slate-100 px-3 py-1 text-xs text-slate-600">
                            hirli.ai/dashboard
                          </div>
                        </div>
                        <div class="flex items-center space-x-1">
                          <div class="h-1.5 w-1.5 animate-pulse rounded-full bg-green-500"></div>
                          <span class="text-xs text-slate-600">Active</span>
                        </div>
                      </div>

                      <!-- Dashboard Content -->
                      <div class="h-full bg-slate-50 p-3">
                        <!-- Stats Cards -->
                        <div class="mb-3 grid grid-cols-3 gap-2">
                          <div class="rounded-lg border border-slate-200 bg-white p-2 shadow-sm">
                            <div class="text-lg font-bold text-slate-900">247</div>
                            <div class="text-xs text-slate-600">Applications</div>
                            <div class="text-xs text-green-600">↗ +23</div>
                          </div>
                          <div class="rounded-lg border border-slate-200 bg-white p-2 shadow-sm">
                            <div class="text-lg font-bold text-slate-900">38</div>
                            <div class="text-xs text-slate-600">Interviews</div>
                            <div class="text-xs text-blue-600">↗ +8</div>
                          </div>
                          <div class="rounded-lg border border-slate-200 bg-white p-2 shadow-sm">
                            <div class="text-lg font-bold text-slate-900">15.4%</div>
                            <div class="text-xs text-slate-600">Response</div>
                            <div class="text-xs text-purple-600">↗ +2.1%</div>
                          </div>
                        </div>

                        <!-- Automation Steps -->
                        <div class="space-y-1">
                          <div
                            class="rounded border border-slate-200/50 bg-white/95 p-2 backdrop-blur-sm
                          {automationStep >= 0
                              ? 'bg-gradient-to-r from-green-50 to-green-100 ring-1 ring-green-300'
                              : ''}">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center text-xs font-medium">
                                <Search class="mr-1 h-3 w-3 text-green-600" />
                                <span>AI Job Scanning</span>
                              </div>
                              <Badge class="bg-green-100 text-xs text-green-700">Active</Badge>
                            </div>
                          </div>

                          <div
                            class="rounded border border-slate-200/50 bg-white/95 p-2 backdrop-blur-sm
                          {automationStep >= 1
                              ? 'bg-gradient-to-r from-blue-50 to-blue-100 ring-1 ring-blue-300'
                              : ''}">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center text-xs font-medium">
                                <Target class="mr-1 h-3 w-3 text-blue-600" />
                                <span>Smart Matching</span>
                              </div>
                              <Badge class="bg-blue-100 text-xs text-blue-700">Processing</Badge>
                            </div>
                          </div>

                          <div
                            class="rounded border border-slate-200/50 bg-white/95 p-2 backdrop-blur-sm
                          {automationStep >= 2
                              ? 'bg-gradient-to-r from-purple-50 to-purple-100 ring-1 ring-purple-300'
                              : ''}">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center text-xs font-medium">
                                <FileText class="mr-1 h-3 w-3 text-purple-600" />
                                <span>Application Generation</span>
                              </div>
                              <Badge class="bg-purple-100 text-xs text-purple-700"
                                >AI-Powered</Badge>
                            </div>
                          </div>

                          <div
                            class="rounded border border-slate-200/50 bg-white/95 p-2 backdrop-blur-sm
                          {automationStep >= 3
                              ? 'bg-gradient-to-r from-green-50 to-green-100 ring-1 ring-green-300'
                              : ''}">
                            <div class="flex items-center justify-between">
                              <div class="flex items-center text-xs font-medium">
                                <Send class="mr-1 h-3 w-3 text-green-600" />
                                <span>Bulk Submission</span>
                              </div>
                              <Badge class="bg-green-100 text-xs text-green-700">Complete</Badge>
                            </div>
                          </div>
                        </div>

                        <!-- Success Indicator -->
                        <div
                          class="absolute bottom-2 left-2 right-2 rounded border border-green-300 bg-green-100 p-2">
                          <div class="flex items-center text-green-800">
                            <Zap class="mr-1 h-3 w-3" />
                            <span class="text-xs font-bold">15 min • 50+ apps • 15% response</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Interactive Slider Handle -->
                    <div
                      class="group absolute bottom-0 top-0 z-20 w-1 cursor-col-resize bg-white/80 shadow-lg backdrop-blur-sm"
                      style="left: {sliderPosition}%"
                      onmousedown={startDrag}
                      onmousemove={handleSliderDrag}
                      onmouseup={stopDrag}
                      onmouseleave={stopDrag}
                      role="slider"
                      tabindex="0"
                      aria-valuenow={sliderPosition}
                      aria-label="Drag to compare manual vs automated job hunting">
                      <!-- Slider Handle -->
                      <div
                        class="absolute left-1/2 top-1/2 flex h-6 w-6 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full border-2 border-slate-300 bg-white shadow-xl transition-transform group-hover:scale-110">
                        <ArrowLeftRight class="h-3 w-3 text-slate-600" />
                      </div>

                      <!-- Labels -->
                      <div
                        class="absolute -top-6 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-black/70 px-2 py-1 text-xs font-medium text-white">
                        Drag to compare
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Right Column - Additional Images -->
                <div class="flex flex-col gap-6 sm:gap-8">
                  <div
                    class="relative overflow-hidden rounded-xl bg-slate-900/5 ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    <img
                      alt="Team collaboration"
                      src="https://images.unsplash.com/photo-1485217988980-11786ced9454?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&h=528&q=80"
                      class="h-80 w-full object-cover object-center lg:h-96" />
                    <div
                      class="absolute inset-0 rounded-xl ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    </div>
                  </div>
                  <div
                    class="relative overflow-hidden rounded-xl bg-slate-900/5 ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    <img
                      alt="Success metrics"
                      src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&crop=focalpoint&fp-x=.4&w=396&h=528&q=80"
                      class="h-80 w-full object-cover object-center lg:h-96" />
                    <div
                      class="absolute inset-0 rounded-xl ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    </div>
                  </div>
                </div>

                <!-- Third Column -->
                <div class="flex flex-col gap-6 sm:gap-8">
                  <div
                    class="relative overflow-hidden rounded-xl bg-slate-900/5 ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    <img
                      alt="AI automation"
                      src="https://images.unsplash.com/photo-1670272504528-790c24957dda?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&crop=left&w=400&h=528&q=80"
                      class="h-80 w-full object-cover object-center lg:h-96" />
                    <div
                      class="absolute inset-0 rounded-xl ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    </div>
                  </div>
                  <div
                    class="relative overflow-hidden rounded-xl bg-slate-900/5 ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    <img
                      alt="Job search results"
                      src="https://images.unsplash.com/photo-1670272505284-8faba1c31f7d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDF8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&h=528&q=80"
                      class="h-80 w-full object-cover object-center lg:h-96" />
                    <div
                      class="absolute inset-0 rounded-xl ring-1 ring-inset ring-slate-900/10 lg:rounded-2xl">
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/if}
      </div>
    </div>
  </div>
</section>
