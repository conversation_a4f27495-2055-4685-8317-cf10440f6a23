<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import {
    ArrowUpRight,
    Users,
    Clock,
    Target,
    ChevronRight,
    Play,
    CheckCircle,
    Sparkles,
    Zap,
    TrendingUp,
    Shield,
    Bot,
    FileText,
    Search,
  } from 'lucide-svelte';
</script>

<style>
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }
</style>

<section class="min-h-screen bg-gray-50">
  <div class="grid min-h-screen lg:grid-cols-2">
    <!-- Left side - Content with floating elements -->
    <div class="relative flex flex-col justify-between overflow-hidden bg-white p-12 lg:p-16">
      <!-- Floating design elements -->
      <div
        class="absolute right-8 top-20 h-32 w-32 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 opacity-60 blur-xl">
      </div>
      <div
        class="absolute bottom-40 left-8 h-24 w-24 rounded-full bg-gradient-to-br from-purple-100 to-pink-100 opacity-40 blur-lg">
      </div>

      <!-- Header -->
      <header class="relative z-10 flex items-center justify-between">
        <div class="text-xl font-bold text-gray-900">AutoApply</div>
        <nav class="hidden items-center gap-6 text-sm md:flex">
          <a href="#features" class="text-gray-600 transition-colors hover:text-gray-900"
            >Features</a>
          <a href="#pricing" class="text-gray-600 transition-colors hover:text-gray-900">Pricing</a>
          <Button
            size="sm"
            variant="outline"
            class="border-gray-300 hover:border-blue-500 hover:text-blue-600">
            Sign In
          </Button>
        </nav>
      </header>

      <!-- Main content -->
      <div class="relative z-10 max-w-lg space-y-12">
        <div class="space-y-8">
          <!-- Animated badge -->
          <div
            class="group inline-flex cursor-pointer items-center gap-3 rounded-full border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3 text-sm font-medium text-blue-700 transition-all duration-300 hover:from-blue-100 hover:to-indigo-100">
            <Sparkles class="h-4 w-4 transition-transform group-hover:rotate-12" />
            <span>AI-Powered Job Application Revolution</span>
            <ChevronRight class="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </div>

          <h1 class="text-[clamp(2.5rem,5vw,4.5rem)] font-bold leading-[1.1] text-gray-900">
            Automate Your
            <br />
            <span class="relative">
              Job Search
              <div
                class="absolute -bottom-2 left-0 h-3 w-full -skew-x-12 bg-gradient-to-r from-blue-200 to-indigo-200 opacity-70">
              </div>
            </span>
          </h1>

          <p class="text-lg leading-relaxed text-gray-600">
            Apply to hundreds of jobs automatically with our AI-powered platform. Smart matching,
            personalized applications, and real-time tracking. Land your dream job 10x faster.
          </p>
        </div>

        <!-- Enhanced key benefits with checkmarks -->
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-green-100">
              <CheckCircle class="h-4 w-4 text-green-600" />
            </div>
            <span class="font-medium text-gray-700">Apply to 100+ jobs in minutes, not hours</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100">
              <CheckCircle class="h-4 w-4 text-blue-600" />
            </div>
            <span class="font-medium text-gray-700"
              >AI-powered resume and cover letter matching</span>
          </div>
          <div class="flex items-center gap-4">
            <div class="flex h-6 w-6 items-center justify-center rounded-full bg-purple-100">
              <CheckCircle class="h-4 w-4 text-purple-600" />
            </div>
            <span class="font-medium text-gray-700"
              >Real-time application tracking and analytics</span>
          </div>
        </div>

        <!-- Creative CTA section -->
        <div class="space-y-6">
          <div class="flex flex-col gap-4 sm:flex-row">
            <Button
              size="lg"
              class="group relative overflow-hidden rounded-xl bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-4 text-base font-semibold text-white hover:from-blue-700 hover:to-indigo-700">
              <div
                class="absolute inset-0 bg-gradient-to-r from-blue-700 to-indigo-700 opacity-0 transition-opacity group-hover:opacity-100">
              </div>
              <span class="relative flex items-center gap-2">
                Start Free Trial
                <ArrowUpRight
                  class="h-4 w-4 transition-transform group-hover:-translate-y-1 group-hover:translate-x-1" />
              </span>
            </Button>
            <button
              class="group flex items-center gap-3 px-4 text-sm text-gray-600 transition-colors hover:text-gray-900">
              <div
                class="flex h-12 w-12 items-center justify-center rounded-full border-2 border-gray-300 transition-all group-hover:border-blue-500 group-hover:bg-blue-50">
                <Play class="ml-0.5 h-5 w-5 group-hover:text-blue-600" />
              </div>
              <div class="text-left">
                <div class="font-medium">Watch Demo</div>
                <div class="text-xs text-gray-500">2 min overview</div>
              </div>
            </button>
          </div>

          <div class="flex items-center gap-4 text-sm text-gray-500">
            <div class="flex items-center gap-2">
              <div class="h-2 w-2 rounded-full bg-green-500"></div>
              <span>No credit card required</span>
            </div>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Setup in under 2 minutes</span>
            <div class="h-1 w-1 rounded-full bg-gray-300"></div>
            <span>Cancel anytime</span>
          </div>
        </div>
      </div>

      <!-- Enhanced footer with social proof -->
      <div class="relative z-10 flex items-end justify-between pt-8">
        <div class="space-y-4">
          <div class="text-sm font-medium text-gray-900">
            Trusted by 10,000+ job seekers worldwide
          </div>
          <div class="flex items-center gap-8">
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">50K+</div>
              <div class="text-xs text-gray-500">Applications sent</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">85%</div>
              <div class="text-xs text-gray-500">Success rate</div>
            </div>
            <div class="text-center">
              <div class="text-xl font-bold text-gray-900">2.5x</div>
              <div class="text-xs text-gray-500">Faster hiring</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Enhanced visual with interactive elements -->
    <div
      class="relative overflow-hidden bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900">
      <!-- Animated background pattern -->
      <div class="absolute inset-0 opacity-10">
        <div
          class="h-full w-full"
          style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.2) 1px, transparent 1px), radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 60px 60px;">
        </div>
      </div>

      <!-- Floating geometric shapes -->
      <div
        class="absolute right-12 top-1/4 h-32 w-32 rotate-12 animate-pulse rounded-3xl border border-white/20">
      </div>
      <div class="absolute bottom-1/3 left-12 h-24 w-24 rotate-45 rounded-2xl bg-blue-500/30"></div>
      <div class="absolute left-1/3 top-1/2 h-16 w-16 rounded-full border-2 border-indigo-400/40">
      </div>

      <!-- Content overlay with enhanced cards -->
      <div class="relative flex h-full flex-col justify-between p-12 text-white lg:p-16">
        <div class="space-y-8">
          <div class="space-y-4">
            <div class="text-4xl font-bold">10,000+</div>
            <div class="text-blue-200">Job seekers automated their search</div>
            <div class="h-1 w-16 bg-gradient-to-r from-blue-400 to-indigo-400"></div>
          </div>

          <div class="space-y-6">
            <!-- Enhanced testimonial card -->
            <div
              class="group cursor-pointer rounded-2xl border border-white/20 bg-white/10 p-6 backdrop-blur-lg transition-all hover:bg-white/15">
              <div class="mb-4 flex items-center gap-4">
                <div
                  class="flex h-14 w-14 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-500 transition-transform group-hover:scale-110">
                  <Bot class="h-7 w-7 text-white" />
                </div>
                <div>
                  <div class="text-lg font-semibold">Smart Automation</div>
                  <div class="text-sm text-blue-200">AI-powered job matching</div>
                </div>
              </div>
              <div class="mb-4 text-gray-200">
                "Went from 5 applications per week to 50+ per day. Got 3 interviews in the first
                week. This platform is incredible!"
              </div>
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-sm font-medium">Alex Rodriguez</div>
                  <div class="text-xs text-blue-200">Software Engineer</div>
                </div>
                <div class="flex">
                  {#each Array(5) as _, i}
                    <div class="h-4 w-4 text-yellow-400">★</div>
                  {/each}
                </div>
              </div>
            </div>

            <!-- ROI highlight card -->
            <div
              class="rounded-2xl border border-green-400/30 bg-gradient-to-r from-green-500/20 to-emerald-500/20 p-6 backdrop-blur-lg">
              <div class="mb-2 text-sm font-medium text-green-200">AVERAGE TIME SAVED</div>
              <div class="mb-2 text-3xl font-bold text-white">20+ hrs/week</div>
              <div class="text-sm text-green-200">Focus on interviews, not applications</div>
            </div>
          </div>
        </div>

        <!-- Bottom metrics grid -->
        <div class="space-y-6">
          <div class="grid grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-2xl font-bold">500K+</div>
              <div class="text-xs uppercase tracking-wider text-blue-200">Jobs scanned daily</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold">99.9%</div>
              <div class="text-xs uppercase tracking-wider text-blue-200">Uptime</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold">24/7</div>
              <div class="text-xs uppercase tracking-wider text-blue-200">Auto-apply</div>
            </div>
          </div>

          <div class="h-[1px] bg-gradient-to-r from-transparent via-white/30 to-transparent"></div>

          <div class="space-y-3">
            <div class="text-sm font-medium text-blue-200">Security & Privacy</div>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div class="flex items-center justify-between">
                <span>Data Encrypted</span>
                <span class="font-bold text-green-400">✓</span>
              </div>
              <div class="flex items-center justify-between">
                <span>GDPR Compliant</span>
                <span class="font-bold text-green-400">✓</span>
              </div>
              <div class="flex items-center justify-between">
                <span>No Data Selling</span>
                <span class="font-bold text-green-400">✓</span>
              </div>
              <div class="flex items-center justify-between">
                <span>Full Control</span>
                <span class="font-bold text-green-400">✓</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
